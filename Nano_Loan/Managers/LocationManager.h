#ifndef LocationManager_h
#define LocationManager_h

#import <Foundation/Foundation.h>
#import <CoreLocation/CoreLocation.h>

NS_ASSUME_NONNULL_BEGIN

/// 定位完成回调
typedef void(^LocationCompletionBlock)(CLLocationCoordinate2D coordinate, NSError * _Nullable error);

/// 高精度埋点定位管理类
@interface LocationManager : NSObject

+ (instancetype)sharedManager;

/// 高精度埋点定位
/// @param completion 定位完成回调
- (void)getCurrentLocationForTracking:(LocationCompletionBlock)completion;

/// 获取缓存的经纬度坐标
- (CLLocationCoordinate2D)getCachedCoordinate;

/// 强制刷新定位缓存 - 用于需要最新定位的场景
- (void)forceRefreshLocationCache:(LocationCompletionBlock)completion;

/// 风控埋点专用高精度定位 - 强制获取最新位置，不使用缓存
- (void)getCurrentLocationForRiskEvent:(LocationCompletionBlock)completion;

/// 上报位置信息到服务器
- (void)reportLocationInfo;

/// 测试地址逆编码功能（用于调试越南坐标问题）
- (void)testGeocoding:(CLLocationCoordinate2D)coordinate;

@end

NS_ASSUME_NONNULL_END

#endif /* LocationManager_h */