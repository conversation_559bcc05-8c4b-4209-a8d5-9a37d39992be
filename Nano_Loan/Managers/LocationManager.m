#import "LocationManager.h"
#import "NetworkManager.h"
#import <CoreLocation/CoreLocation.h>

static NSString * const kCachedCoordinateKey = @"CachedCoordinate";
static const NSTimeInterval kLocationTimeout = 15.0; // 15秒超时，快速获取位置
static const CLLocationAccuracy kBestAccuracy = 0.1; // 最佳精度0.1米（超高精度）
static const CLLocationAccuracy kGoodAccuracy = 0.3; // 良好精度0.3米
static const CLLocationAccuracy kAcceptableAccuracy = 1.0; // 可接受精度1米（降低门槛）
static const NSTimeInterval kLocationMaxAge = 0.5; // 位置数据最大有效期0.5秒（确保极高新鲜度）
static const NSTimeInterval kMinWaitTime = 3.0; // 最少等待3秒，快速响应位置变化

/// 单次定位请求对象
@interface LocationRequest : NSObject
@property (nonatomic, strong) CLLocationManager *locationManager;
@property (nonatomic, copy) LocationCompletionBlock completion;
@property (nonatomic, strong) NSTimer *timeoutTimer;
@property (nonatomic, assign) NSTimeInterval startTime;
@property (nonatomic, strong) CLLocation *bestLocation; // 当前最佳位置
@property (nonatomic, assign) BOOL hasReceivedFirstLocation; // 是否已收到第一个位置
@property (nonatomic, assign) NSInteger locationUpdateCount; // 位置更新次数
@end

@implementation LocationRequest
@end

@interface LocationManager () <CLLocationManagerDelegate>
@property (nonatomic, strong) NSMutableArray<LocationRequest *> *activeRequests;
@property (nonatomic, assign) CLLocationCoordinate2D cachedCoordinate;
@property (nonatomic, assign) NSTimeInterval lastReportTime; // 上次地址埋点上报时间
@end

@implementation LocationManager

+ (instancetype)sharedManager {
    static LocationManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[LocationManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _activeRequests = [NSMutableArray array];
        _cachedCoordinate = [self loadCachedCoordinate];
    }
    return self;
}

#pragma mark - Public Methods

- (void)getCurrentLocationForTracking:(LocationCompletionBlock)completion {
    if (!completion) {
        return;
    }

    // 检查定位权限
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    if (status == kCLAuthorizationStatusDenied || status == kCLAuthorizationStatusRestricted) {
        CLLocationCoordinate2D coordinate = CLLocationCoordinate2DIsValid(self.cachedCoordinate) ? self.cachedCoordinate : kCLLocationCoordinate2DInvalid;
        completion(coordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
        return;
    }

    // 创建新的定位请求
    LocationRequest *request = [[LocationRequest alloc] init];
    request.completion = completion;
    request.startTime = [[NSDate date] timeIntervalSince1970];

    // 创建新的CLLocationManager实例，配置最高精度
    request.locationManager = [[CLLocationManager alloc] init];
    request.locationManager.delegate = self;
    request.locationManager.desiredAccuracy = kCLLocationAccuracyBestForNavigation; // 最高精度
    request.locationManager.distanceFilter = kCLDistanceFilterNone; // 不过滤任何距离变化，捕获微小移动

    // iOS 14+ 临时精确位置权限
    if (@available(iOS 14.0, *)) {
        if (request.locationManager.accuracyAuthorization == CLAccuracyAuthorizationReducedAccuracy) {
            [request.locationManager requestTemporaryFullAccuracyAuthorizationWithPurposeKey:@"tracking" completion:nil];
        }
    }

    // 设置超时定时器
    __weak typeof(self) weakSelf = self;
    __weak typeof(request) weakRequest = request;
    request.timeoutTimer = [NSTimer scheduledTimerWithTimeInterval:kLocationTimeout repeats:NO block:^(NSTimer * _Nonnull timer) {
        [weakSelf handleLocationTimeout:weakRequest];
    }];

    // 添加到活跃请求列表
    [self.activeRequests addObject:request];

    // 请求权限（如果需要）并开始定位
    if (status == kCLAuthorizationStatusNotDetermined) {
        [request.locationManager requestWhenInUseAuthorization];
    } else {
        [request.locationManager startUpdatingLocation];
    }
}

- (CLLocationCoordinate2D)getCachedCoordinate {
    return self.cachedCoordinate;
}

/// 强制刷新定位缓存 - 用于需要最新定位的场景
- (void)forceRefreshLocationCache:(LocationCompletionBlock)completion {
    [self getCurrentLocationForTracking:completion];
}

- (void)getCurrentLocationForRiskEvent:(LocationCompletionBlock)completion {
    // 风控埋点专用：强制获取最新高精度位置，不使用任何缓存
    NSLog(@"[埋点测试8-2] 风控埋点开始获取最新位置");

    LocationRequest *request = [[LocationRequest alloc] init];
    request.completion = completion;
    request.startTime = [[NSDate date] timeIntervalSince1970];

    // 创建新的CLLocationManager实例，配置超高精度
    request.locationManager = [[CLLocationManager alloc] init];
    request.locationManager.delegate = self;
    request.locationManager.desiredAccuracy = kCLLocationAccuracyBestForNavigation; // 最高精度
    request.locationManager.distanceFilter = kCLDistanceFilterNone; // 捕获任何距离变化

    // iOS 14+ 临时精确位置权限
    if (@available(iOS 14.0, *)) {
        if (request.locationManager.accuracyAuthorization == CLAccuracyAuthorizationReducedAccuracy) {
            [request.locationManager requestTemporaryFullAccuracyAuthorizationWithPurposeKey:@"risk_tracking" completion:nil];
        }
    }

    [self.activeRequests addObject:request];

    // 检查定位权限
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    if (status == kCLAuthorizationStatusNotDetermined) {
        [request.locationManager requestWhenInUseAuthorization];
    } else if (status == kCLAuthorizationStatusDenied || status == kCLAuthorizationStatusRestricted) {
        NSError *error = [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location access denied"}];
        [self completeRequest:request withCoordinate:kCLLocationCoordinate2DInvalid error:error];
        return;
    } else {
        // 立即开始定位
        [request.locationManager startUpdatingLocation];

        // 设置超时定时器（风控埋点使用更短的超时时间）
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self handleRequestTimeout:request];
        });
    }
}

- (void)reportLocationInfo {
    // 地址埋点防重复：如果距离上次上报不足5秒，则跳过
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    if (currentTime - self.lastReportTime < 5.0) {
        NSLog(@"[埋点测试8-2] 地址埋点防重复：距离上次上报不足5秒，跳过本次上报");
        return;
    }

    [self getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
        if (error) {
            // 即使定位失败，也尝试使用缓存坐标上报
            coordinate = self.cachedCoordinate;
        }

        if (!CLLocationCoordinate2DIsValid(coordinate)) {
            return;
        }

        // 使用反向地理编码获取详细地址信息
        CLGeocoder *geocoder = [[CLGeocoder alloc] init];
        CLLocation *location = [[CLLocation alloc] initWithLatitude:coordinate.latitude longitude:coordinate.longitude];

        [geocoder reverseGeocodeLocation:location completionHandler:^(NSArray<CLPlacemark *> * _Nullable placemarks, NSError * _Nullable geocodeError) {
            //这里有问题
            NSString *province = @"";     // 省
            NSString *city = @"";         // 市
            NSString *district = @"";     // 区
            NSString *street = @"";       // 街道
            NSString *country = @""; // 国家
            NSString *countryCode = @""; // 国家代码

            if (geocodeError) {
                // 反向地理编码失败，使用默认值
            } else if (placemarks.count > 0) {
                CLPlacemark *placemark = placemarks.firstObject;

                // 提取地址信息
                province = placemark.administrativeArea ?: @"";           // 省/州
                city = placemark.locality ?: placemark.subAdministrativeArea ?: @""; // 市
                district = placemark.subLocality ?: @"";                 // 区/县
                street = placemark.thoroughfare ?: placemark.name ?: @""; // 街道
                country = placemark.country ?: @"";                 // 国家
                countryCode = placemark.ISOcountryCode ?: @"";         // 国家代码
            }

            // 构建上报参数
            NSDictionary *params = @{
                @"darrein": province,                    // 省
                @"italways": countryCode,                // 国家code
                @"unkindly": country,                    // 国家
                @"askedme": street,                      // 街道
                @"invitation": [NSString stringWithFormat:@"%.6f", coordinate.latitude],   // 纬度
                @"hersuch": [NSString stringWithFormat:@"%.6f", coordinate.longitude],     // 经度
                @"sharethe": city,                       // 市
                @"adoration": district                   // 区
            };

            // 更新上次上报时间
            self.lastReportTime = [[NSDate date] timeIntervalSince1970];

            NSLog(@"[埋点测试8-2] 埋点-地址，使用的经度%.6f，纬度%.6f", coordinate.longitude, coordinate.latitude);

            [NetworkManager postFormWithAPI:@"Alicia/cardboardcontainers" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable networkError) {
                if (!networkError) {
                    NSLog(@"[埋点测试8-2] 埋点-地址 上报成功");
                }
            }];
        }];
    }];
}

#pragma mark - Private Methods

- (CLLocationCoordinate2D)loadCachedCoordinate {
    NSData *data = [[NSUserDefaults standardUserDefaults] objectForKey:kCachedCoordinateKey];
    if (data) {
        CLLocationCoordinate2D coordinate;
        [data getBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        if (CLLocationCoordinate2DIsValid(coordinate)) {
            return coordinate;
        }
    }
    // 返回无效坐标，表示没有缓存数据
    return kCLLocationCoordinate2DInvalid;
}

- (void)saveCachedCoordinate:(CLLocationCoordinate2D)coordinate {
    if (CLLocationCoordinate2DIsValid(coordinate)) {
        NSData *data = [NSData dataWithBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        [[NSUserDefaults standardUserDefaults] setObject:data forKey:kCachedCoordinateKey];
        [[NSUserDefaults standardUserDefaults] synchronize];
        self.cachedCoordinate = coordinate;
    }
}

- (void)handleLocationTimeout:(LocationRequest *)request {
    if (!request || ![self.activeRequests containsObject:request]) {
        return;
    }

    // 停止定位
    [request.locationManager stopUpdatingLocation];

    CLLocationCoordinate2D finalCoordinate;
    NSError *error = nil;

    // 优先使用本次定位过程中获得的最佳位置
    if (request.bestLocation && CLLocationCoordinate2DIsValid(request.bestLocation.coordinate)) {
        finalCoordinate = request.bestLocation.coordinate;
        [self saveCachedCoordinate:finalCoordinate];

        if (request.bestLocation.horizontalAccuracy > kAcceptableAccuracy) {
            error = [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Timeout with low accuracy: %.1fm", request.bestLocation.horizontalAccuracy]}];
        }
    } else {
        // 使用缓存坐标兜底
        if (CLLocationCoordinate2DIsValid(self.cachedCoordinate)) {
            finalCoordinate = self.cachedCoordinate;
            error = [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: @"Location timeout, using cached coordinate"}];
        } else {
            finalCoordinate = kCLLocationCoordinate2DInvalid;
            error = [NSError errorWithDomain:@"LocationManager" code:-3 userInfo:@{NSLocalizedDescriptionKey: @"Location timeout, no valid coordinate available"}];
        }
    }

    // 回调结果
    if (request.completion) {
        request.completion(finalCoordinate, error);
    }

    // 清理请求
    [self cleanupRequest:request];
}

- (void)cleanupRequest:(LocationRequest *)request {
    if (!request) {
        return;
    }

    // 取消定时器
    if (request.timeoutTimer) {
        [request.timeoutTimer invalidate];
        request.timeoutTimer = nil;
    }

    // 停止定位
    [request.locationManager stopUpdatingLocation];
    request.locationManager.delegate = nil;

    // 从活跃请求列表中移除
    [self.activeRequests removeObject:request];
}

- (LocationRequest *)findRequestForLocationManager:(CLLocationManager *)manager {
    for (LocationRequest *request in self.activeRequests) {
        if (request.locationManager == manager) {
            return request;
        }
    }
    return nil;
}

#pragma mark - CLLocationManagerDelegate

- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray<CLLocation *> *)locations {
    CLLocation *location = locations.lastObject;
    if (!location) {
        return;
    }

    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        return;
    }

    // 检查位置数据的新鲜度
    NSTimeInterval locationAge = -[location.timestamp timeIntervalSinceNow];
    if (locationAge > kLocationMaxAge) {
        return;
    }

    // 检查位置精度有效性
    if (location.horizontalAccuracy < 0) {
        return;
    }

    request.locationUpdateCount++;
    NSTimeInterval elapsedTime = [[NSDate date] timeIntervalSince1970] - request.startTime;

    // 智能精度判断逻辑
    BOOL shouldAcceptLocation = [self shouldAcceptLocation:location forRequest:request elapsedTime:elapsedTime];

    if (!shouldAcceptLocation) {
        // 更新最佳位置记录
        if (!request.bestLocation || location.horizontalAccuracy < request.bestLocation.horizontalAccuracy) {
            request.bestLocation = location;
        }
        return;
    }

    // 选择最终位置
    CLLocation *finalLocation = request.bestLocation && request.bestLocation.horizontalAccuracy < location.horizontalAccuracy ? request.bestLocation : location;

    // 保存到缓存
    [self saveCachedCoordinate:finalLocation.coordinate];

    // 回调成功结果
    if (request.completion) {
        request.completion(finalLocation.coordinate, nil);
    }

    // 清理请求
    [self cleanupRequest:request];
}

- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error {
    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        return;
    }

    // 使用缓存坐标回调
    if (request.completion) {
        CLLocationCoordinate2D coordinate = CLLocationCoordinate2DIsValid(self.cachedCoordinate) ? self.cachedCoordinate : kCLLocationCoordinate2DInvalid;
        request.completion(coordinate, error);
    }

    // 清理请求
    [self cleanupRequest:request];
}

- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        return;
    }

    switch (status) {
        case kCLAuthorizationStatusAuthorizedWhenInUse:
        case kCLAuthorizationStatusAuthorizedAlways:
            [manager startUpdatingLocation];
            break;
        case kCLAuthorizationStatusDenied:
        case kCLAuthorizationStatusRestricted:
            if (request.completion) {
                CLLocationCoordinate2D coordinate = CLLocationCoordinate2DIsValid(self.cachedCoordinate) ? self.cachedCoordinate : kCLLocationCoordinate2DInvalid;
                request.completion(coordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
            }
            [self cleanupRequest:request];
            break;
        case kCLAuthorizationStatusNotDetermined:
            break;
        default:
            break;
    }
}

#pragma mark - Helper Methods

/// 智能判断是否应该接受当前位置 - 针对高精度实时定位优化
- (BOOL)shouldAcceptLocation:(CLLocation *)location forRequest:(LocationRequest *)request elapsedTime:(NSTimeInterval)elapsedTime {
    // 第一次收到位置
    if (!request.hasReceivedFirstLocation) {
        request.hasReceivedFirstLocation = YES;
    }

    // 立即接受的条件：亚米级精度（0.5米以内）
    if (location.horizontalAccuracy <= kBestAccuracy) {
        return YES;
    }

    // 良好精度 + 等待时间足够 + 收到少量更新（降低门槛）
    if (location.horizontalAccuracy <= kGoodAccuracy && elapsedTime >= kMinWaitTime && request.locationUpdateCount >= 2) {
        return YES;
    }

    // 可接受精度 + 等待时间较短 + 收到更新（大幅降低门槛）
    if (location.horizontalAccuracy <= kAcceptableAccuracy && elapsedTime >= kMinWaitTime && request.locationUpdateCount >= 2) {
        return YES;
    }

    // 超过70%超时时间，使用最佳可用位置（更早接受位置）
    if (elapsedTime >= (kLocationTimeout * 0.7)) {
        return YES;
    }

    // 如果已经等待了5秒且有任何有效位置，就接受（新增快速接受逻辑）
    if (elapsedTime >= 5.0 && location.horizontalAccuracy <= 5.0) {
        return YES;
    }

    // 继续等待更好的精度
    return NO;
}

#pragma mark - Missing Methods

- (void)completeRequest:(LocationRequest *)request withCoordinate:(CLLocationCoordinate2D)coordinate error:(NSError *)error {
    // 完成单个请求
    if (![self.activeRequests containsObject:request]) {
        return; // 请求已经完成或不存在
    }

    // 停止定位
    [request.locationManager stopUpdatingLocation];

    // 从活跃请求列表中移除
    [self.activeRequests removeObject:request];

    // 执行回调
    if (request.completion) {
        request.completion(coordinate, error);
    }
}

- (void)handleRequestTimeout:(LocationRequest *)request {
    // 处理请求超时
    if (![self.activeRequests containsObject:request]) {
        return; // 请求已经完成
    }

    [request.locationManager stopUpdatingLocation];

    // 优先使用本次定位过程中获得的最佳位置
    CLLocationCoordinate2D finalCoordinate = kCLLocationCoordinate2DInvalid;
    NSError *error = nil;

    if (request.bestLocation && CLLocationCoordinate2DIsValid(request.bestLocation.coordinate)) {
        finalCoordinate = request.bestLocation.coordinate;
        [self saveCachedCoordinate:finalCoordinate];

        if (request.bestLocation.horizontalAccuracy > kAcceptableAccuracy) {
            error = [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Timeout with low accuracy: %.1fm", request.bestLocation.horizontalAccuracy]}];
        }
    } else {
        // 使用缓存坐标兜底
        if (CLLocationCoordinate2DIsValid(self.cachedCoordinate)) {
            finalCoordinate = self.cachedCoordinate;
        }
        error = [NSError errorWithDomain:@"LocationManager" code:-3 userInfo:@{NSLocalizedDescriptionKey: @"Timeout without valid location"}];
    }

    [self completeRequest:request withCoordinate:finalCoordinate error:error];
}



@end
