//
//  LocationTestViewController.m
//  Nano_Loan
//
//  地址逆编码测试界面
//

#import "LocationTestViewController.h"
#import "LocationManager.h"

@interface LocationTestViewController ()
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *coordinateLabel;
@property (nonatomic, strong) UILabel *systemGeocodeLabel;
@property (nonatomic, strong) UILabel *backupGeocodeLabel;
@property (nonatomic, strong) UILabel *finalAddressLabel;
@property (nonatomic, strong) UILabel *uploadParamsLabel;
@property (nonatomic, strong) UILabel *uploadResultLabel;
@property (nonatomic, strong) UIButton *testButton;
@property (nonatomic, strong) UIButton *closeButton;
@end

@implementation LocationTestViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    
    // 监听LocationManager的测试通知
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(handleLocationTestUpdate:) 
                                                 name:@"LocationTestUpdate" 
                                               object:nil];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor whiteColor];
    
    // 滚动视图
    self.scrollView = [[UIScrollView alloc] initWithFrame:self.view.bounds];
    self.scrollView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:self.scrollView];
    
    CGFloat margin = 20;
    CGFloat currentY = 50;
    CGFloat labelHeight = 30;
    CGFloat width = self.view.bounds.size.width - 2 * margin;
    
    // 标题
    self.titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(margin, currentY, width, labelHeight)];
    self.titleLabel.text = @"🔍 Location Test Debug Panel";
    self.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.titleLabel.textColor = [UIColor colorWithRed:0.2 green:0.6 blue:1.0 alpha:1.0];
    [self.scrollView addSubview:self.titleLabel];
    currentY += labelHeight + 20;
    
    // 关闭按钮
    self.closeButton = [[UIButton alloc] initWithFrame:CGRectMake(width - 60, 10, 80, 30)];
    [self.closeButton setTitle:@"Close" forState:UIControlStateNormal];
    [self.closeButton setTitleColor:[UIColor redColor] forState:UIControlStateNormal];
    self.closeButton.titleLabel.font = [UIFont systemFontOfSize:16];
    [self.closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.closeButton];
    
    // 测试按钮
    self.testButton = [[UIButton alloc] initWithFrame:CGRectMake(margin, currentY, width, 44)];
    [self.testButton setTitle:@"🚀 Start Location Test" forState:UIControlStateNormal];
    [self.testButton setBackgroundColor:[UIColor colorWithRed:0.2 green:0.6 blue:1.0 alpha:1.0]];
    self.testButton.layer.cornerRadius = 8;
    self.testButton.titleLabel.font = [UIFont boldSystemFontOfSize:16];
    [self.testButton addTarget:self action:@selector(startLocationTest) forControlEvents:UIControlEventTouchUpInside];
    [self.scrollView addSubview:self.testButton];
    currentY += 44 + 20;
    
    // 坐标信息
    self.coordinateLabel = [self createInfoLabelWithFrame:CGRectMake(margin, currentY, width, 60) 
                                                    title:@"📍 Current Coordinate" 
                                                  content:@"Waiting for location..."];
    [self.scrollView addSubview:self.coordinateLabel];
    currentY += 60 + 15;
    
    // 系统地理编码结果
    self.systemGeocodeLabel = [self createInfoLabelWithFrame:CGRectMake(margin, currentY, width, 120) 
                                                       title:@"🏛️ System Geocoding Result" 
                                                     content:@"Not started yet"];
    [self.scrollView addSubview:self.systemGeocodeLabel];
    currentY += 120 + 15;
    
    // 备用地理编码结果
    self.backupGeocodeLabel = [self createInfoLabelWithFrame:CGRectMake(margin, currentY, width, 120) 
                                                       title:@"🌐 Backup API Result" 
                                                     content:@"Not started yet"];
    [self.scrollView addSubview:self.backupGeocodeLabel];
    currentY += 120 + 15;
    
    // 最终地址信息
    self.finalAddressLabel = [self createInfoLabelWithFrame:CGRectMake(margin, currentY, width, 120) 
                                                      title:@"✅ Final Address Info" 
                                                    content:@"Not processed yet"];
    [self.scrollView addSubview:self.finalAddressLabel];
    currentY += 120 + 15;
    
    // 上报参数
    self.uploadParamsLabel = [self createInfoLabelWithFrame:CGRectMake(margin, currentY, width, 150) 
                                                      title:@"📤 Upload Parameters" 
                                                    content:@"Not generated yet"];
    [self.scrollView addSubview:self.uploadParamsLabel];
    currentY += 150 + 15;
    
    // 上报结果
    self.uploadResultLabel = [self createInfoLabelWithFrame:CGRectMake(margin, currentY, width, 60) 
                                                      title:@"📊 Upload Result" 
                                                    content:@"Not uploaded yet"];
    [self.scrollView addSubview:self.uploadResultLabel];
    currentY += 60 + 20;
    
    // 设置滚动视图内容大小
    self.scrollView.contentSize = CGSizeMake(width, currentY);
}

- (UILabel *)createInfoLabelWithFrame:(CGRect)frame title:(NSString *)title content:(NSString *)content {
    UILabel *label = [[UILabel alloc] initWithFrame:frame];
    label.numberOfLines = 0;
    label.font = [UIFont systemFontOfSize:12];
    label.textColor = [UIColor darkGrayColor];
    label.backgroundColor = [UIColor colorWithRed:0.95 green:0.95 blue:0.95 alpha:1.0];
    label.layer.cornerRadius = 8;
    label.layer.masksToBounds = YES;
    
    NSString *fullText = [NSString stringWithFormat:@"%@\n%@", title, content];
    NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:fullText];
    
    // 标题样式
    NSRange titleRange = NSMakeRange(0, title.length);
    [attributedText addAttribute:NSFontAttributeName value:[UIFont boldSystemFontOfSize:14] range:titleRange];
    [attributedText addAttribute:NSForegroundColorAttributeName value:[UIColor colorWithRed:0.2 green:0.6 blue:1.0 alpha:1.0] range:titleRange];
    
    label.attributedText = attributedText;
    
    // 添加内边距
    label.textAlignment = NSTextAlignmentLeft;
    UIEdgeInsets padding = UIEdgeInsetsMake(8, 12, 8, 12);
    label.layer.sublayerTransform = CATransform3DMakeTranslation(padding.left, padding.top, 0);
    
    return label;
}

- (void)startLocationTest {
    [self.testButton setTitle:@"🔄 Testing..." forState:UIControlStateNormal];
    self.testButton.enabled = NO;
    
    // 清空之前的结果
    [self updateCoordinateInfo:@"Getting location..."];
    [self updateSystemGeocodeInfo:@"Waiting..."];
    [self updateBackupGeocodeInfo:@"Waiting..."];
    [self updateFinalAddressInfo:@"Processing..."];
    [self updateUploadParamsInfo:@"Preparing..."];
    [self updateUploadResultInfo:@"Pending..."];
    
    // 触发LocationManager的测试
    [[LocationManager sharedManager] startDetailedLocationTest];
}

- (void)closeButtonTapped {
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - Update Methods

- (void)updateCoordinateInfo:(NSString *)info {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self updateLabel:self.coordinateLabel withTitle:@"📍 Current Coordinate" content:info];
    });
}

- (void)updateSystemGeocodeInfo:(NSString *)info {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self updateLabel:self.systemGeocodeLabel withTitle:@"🏛️ System Geocoding Result" content:info];
    });
}

- (void)updateBackupGeocodeInfo:(NSString *)info {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self updateLabel:self.backupGeocodeLabel withTitle:@"🌐 Backup API Result" content:info];
    });
}

- (void)updateFinalAddressInfo:(NSString *)info {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self updateLabel:self.finalAddressLabel withTitle:@"✅ Final Address Info" content:info];
    });
}

- (void)updateUploadParamsInfo:(NSString *)info {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self updateLabel:self.uploadParamsLabel withTitle:@"📤 Upload Parameters" content:info];
    });
}

- (void)updateUploadResultInfo:(NSString *)info {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self updateLabel:self.uploadResultLabel withTitle:@"📊 Upload Result" content:info];
        
        // 测试完成，恢复按钮状态
        [self.testButton setTitle:@"🚀 Start Location Test" forState:UIControlStateNormal];
        self.testButton.enabled = YES;
    });
}

- (void)updateLabel:(UILabel *)label withTitle:(NSString *)title content:(NSString *)content {
    NSString *fullText = [NSString stringWithFormat:@"%@\n%@", title, content];
    NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:fullText];
    
    // 标题样式
    NSRange titleRange = NSMakeRange(0, title.length);
    [attributedText addAttribute:NSFontAttributeName value:[UIFont boldSystemFontOfSize:14] range:titleRange];
    [attributedText addAttribute:NSForegroundColorAttributeName value:[UIColor colorWithRed:0.2 green:0.6 blue:1.0 alpha:1.0] range:titleRange];
    
    // 内容样式
    NSRange contentRange = NSMakeRange(title.length, content.length + 1);
    [attributedText addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:12] range:contentRange];
    [attributedText addAttribute:NSForegroundColorAttributeName value:[UIColor darkGrayColor] range:contentRange];
    
    label.attributedText = attributedText;
}

#pragma mark - Notification Handler

- (void)handleLocationTestUpdate:(NSNotification *)notification {
    NSDictionary *userInfo = notification.userInfo;
    NSString *type = userInfo[@"type"];
    NSString *info = userInfo[@"info"];
    
    if ([type isEqualToString:@"coordinate"]) {
        [self updateCoordinateInfo:info];
    } else if ([type isEqualToString:@"systemGeocode"]) {
        [self updateSystemGeocodeInfo:info];
    } else if ([type isEqualToString:@"backupGeocode"]) {
        [self updateBackupGeocodeInfo:info];
    } else if ([type isEqualToString:@"finalAddress"]) {
        [self updateFinalAddressInfo:info];
    } else if ([type isEqualToString:@"uploadParams"]) {
        [self updateUploadParamsInfo:info];
    } else if ([type isEqualToString:@"uploadResult"]) {
        [self updateUploadResultInfo:info];
    }
}

@end
